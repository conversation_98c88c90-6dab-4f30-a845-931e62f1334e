import { getAddressesByCustomer } from '@/services/customer-addresses';
import {
  <PERSON><PERSON>,
  <PERSON>ton,
  Card,
  Input,
  List,
  Modal,
  Radio,
  Space,
  Typography,
  message,
} from 'antd';
import React, { useEffect, useState } from 'react';
import GaoDeMap from '@/components/GaoDeMap';
import { reverseGeocode } from '@/components/GaoDeMap/utils';

const { Text } = Typography;

interface AddressSelectorProps {
  visible: boolean;
  customerId: number;
  onClose: () => void;
  onSelect: (addressData: {
    addressId?: number;
    address: string;
    addressDetail?: string;
    longitude: number;
    latitude: number;
    addressRemark?: string;
  }) => void;
}

/**
 * 地址选择器组件
 * 支持从用户已保存地址选择或在地图上选择新地址
 */
const AddressSelector: React.FC<AddressSelectorProps> = ({
  visible,
  customerId,
  onClose,
  onSelect,
}) => {
  const [loading, setLoading] = useState(false);
  const [addresses, setAddresses] = useState<API.CustomerAddress[]>([]);
  const [selectedMode, setSelectedMode] = useState<'saved' | 'map'>('saved');
  const [selectedAddressId, setSelectedAddressId] = useState<number>();

  // 地图相关状态
  const [selectedPosition, setSelectedPosition] = useState<{
    longitude: number;
    latitude: number;
    address: string;
  }>();
  const [addressDetail, setAddressDetail] = useState('');
  const [addressRemark, setAddressRemark] = useState('');
  const [mapLoading, setMapLoading] = useState(false);

  // 获取用户地址列表
  const fetchAddresses = async () => {
    if (!customerId) return;

    setLoading(true);
    try {
      const response = await getAddressesByCustomer(customerId);
      if (response.errCode) {
        message.error(response.msg || '获取用户地址失败');
        setAddresses([]);
      } else {
        const addressData = response.data || [];
        setAddresses(addressData);
      }
    } catch (error) {
      console.error('获取用户地址失败:', error);
      message.error('获取用户地址失败');
      setAddresses([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible && customerId) {
      fetchAddresses();
    }
  }, [visible, customerId]);

  // 处理选择已保存的地址
  const handleSelectSavedAddress = () => {
    const selectedAddress = addresses.find(
      (addr) => addr.id === selectedAddressId,
    );
    if (!selectedAddress) {
      message.warning('请选择一个地址');
      return;
    }

    // 转换经纬度为数字类型
    const lng =
      typeof selectedAddress.longitude === 'string'
        ? parseFloat(selectedAddress.longitude)
        : selectedAddress.longitude;
    const lat =
      typeof selectedAddress.latitude === 'string'
        ? parseFloat(selectedAddress.latitude)
        : selectedAddress.latitude;

    // 检查是否有经纬度信息
    if (!lng || !lat || isNaN(lng) || isNaN(lat)) {
      message.warning(
        '该地址缺少经纬度信息，请提醒用户到地址管理界面通过点选地图补全信息',
      );
      return;
    }

    // 正确处理地址字段映射
    const mainAddress =
      selectedAddress.addressText || selectedAddress.address || '';
    const detailAddress = selectedAddress.detailAddress;

    // 如果详细地址与主地址相同，则清空详细地址避免重复
    const finalDetailAddress =
      detailAddress === mainAddress ? '' : detailAddress;

    onSelect({
      addressId: selectedAddress.id,
      address: mainAddress,
      addressDetail: finalDetailAddress,
      longitude: lng,
      latitude: lat,
      addressRemark: selectedAddress.addressRemark || selectedAddress.remark,
    });
  };

  // 处理地图点击事件
  const handleMapClick = async (e: any) => {
    const { lng, lat } = e.lnglat;
    setMapLoading(true);

    try {
      // 逆地理编码获取地址
      const address = await reverseGeocode(lng, lat);
      setSelectedPosition({
        longitude: lng,
        latitude: lat,
        address,
      });
      message.success('地址选择成功');
    } catch (error) {
      console.error('地址解析失败:', error);
      message.error('地址解析失败，请重新选择');
    } finally {
      setMapLoading(false);
    }
  };



  // 处理确认选择
  const handleConfirm = () => {
    if (selectedMode === 'saved') {
      handleSelectSavedAddress();
    } else {
      // 地图模式：确认选择地图上的位置
      if (!selectedPosition) {
        message.warning('请在地图上点击选择地址');
        return;
      }

      onSelect({
        address: selectedPosition.address,
        addressDetail: addressDetail.trim() || undefined,
        longitude: selectedPosition.longitude,
        latitude: selectedPosition.latitude,
        addressRemark: addressRemark.trim() || undefined,
      });
    }
  };

  // 过滤有效地址（有经纬度的地址）
  const validAddresses = addresses.filter((addr) => {
    const lng =
      typeof addr.longitude === 'string'
        ? parseFloat(addr.longitude)
        : addr.longitude;
    const lat =
      typeof addr.latitude === 'string'
        ? parseFloat(addr.latitude)
        : addr.latitude;
    const isValid = lng && lat && !isNaN(lng) && !isNaN(lat);
    // console.log('地址验证:', {
    //   id: addr.id,
    //   addressText: addr.addressText,
    //   longitude: addr.longitude,
    //   latitude: addr.latitude,
    //   parsedLng: lng,
    //   parsedLat: lat,
    //   isValid
    // }); // 调试信息 - 生产环境请移除
    return isValid;
  });
  const invalidAddresses = addresses.filter((addr) => {
    const lng =
      typeof addr.longitude === 'string'
        ? parseFloat(addr.longitude)
        : addr.longitude;
    const lat =
      typeof addr.latitude === 'string'
        ? parseFloat(addr.latitude)
        : addr.latitude;
    return !lng || !lat || isNaN(lng) || isNaN(lat);
  });

  // console.log('地址过滤结果:', {
  //   total: addresses.length,
  //   valid: validAddresses.length,
  //   invalid: invalidAddresses.length
  // }); // 调试信息 - 生产环境请移除

  return (
    <>
      <Modal
        title="选择服务地址"
        open={visible}
        onCancel={() => {
          // 重置状态
          setSelectedAddressId(undefined);
          setSelectedPosition(undefined);
          setAddressDetail('');
          setAddressRemark('');
          onClose();
        }}
        width={600}
        footer={[
          <Button key="cancel" onClick={onClose}>
            取消
          </Button>,
          <Button key="confirm" type="primary" onClick={handleConfirm}>
            确认选择
          </Button>,
        ]}
      >
        <Space direction="vertical" style={{ width: '100%' }} size="large">
          <Radio.Group
            value={selectedMode}
            onChange={(e) => setSelectedMode(e.target.value)}
          >
            <Radio value="saved">选择用户已保存的地址</Radio>
            <Radio value="map">在地图上选择新地址</Radio>
          </Radio.Group>

          {selectedMode === 'saved' && (
            <div>
              {loading ? (
                <div style={{ textAlign: 'center', padding: '20px' }}>
                  加载中...
                </div>
              ) : (
                <>
                  {validAddresses.length > 0 ? (
                    <List
                      dataSource={validAddresses}
                      renderItem={(address) => (
                        <List.Item>
                          <Card
                            size="small"
                            style={{
                              width: '100%',
                              backgroundColor:
                                selectedAddressId === address.id
                                  ? '#f6ffed'
                                  : undefined,
                              border:
                                selectedAddressId === address.id
                                  ? '1px solid #52c41a'
                                  : undefined,
                              cursor: 'pointer',
                            }}
                            onClick={() => setSelectedAddressId(address.id)}
                          >
                            <Space
                              direction="vertical"
                              size="small"
                              style={{ width: '100%' }}
                            >
                              <div>
                                <Text strong>{address.name || '地址'}</Text>
                                {address.isDefault && (
                                  <Text
                                    type="secondary"
                                    style={{ marginLeft: 8, fontSize: '12px' }}
                                  >
                                    [默认]
                                  </Text>
                                )}
                              </div>
                              <div>
                                <Text>
                                  {address.addressText || address.address}
                                </Text>
                              </div>
                              {address.addressDetail &&
                                address.addressDetail !==
                                  (address.addressText || address.address) && (
                                  <div>
                                    <Text type="secondary">
                                      详细地址: {address.addressDetail}
                                    </Text>
                                  </div>
                                )}
                              {(address.addressRemark || address.remark) && (
                                <div>
                                  <Text type="secondary">
                                    备注:{' '}
                                    {address.addressRemark || address.remark}
                                  </Text>
                                </div>
                              )}
                              <div>
                                <Text
                                  type="secondary"
                                  style={{ fontSize: '12px' }}
                                >
                                  经纬度: {address.longitude},{' '}
                                  {address.latitude}
                                </Text>
                              </div>
                            </Space>
                          </Card>
                        </List.Item>
                      )}
                    />
                  ) : (
                    <div style={{ textAlign: 'center', padding: '20px' }}>
                      <Text type="secondary">该用户暂无可用地址</Text>
                    </div>
                  )}

                  {invalidAddresses.length > 0 && (
                    <Alert
                      type="warning"
                      message={`发现 ${invalidAddresses.length} 个地址缺少经纬度信息，无法选择`}
                      description="请提醒用户到地址管理界面通过点选地图补全信息"
                      showIcon
                    />
                  )}
                </>
              )}
            </div>
          )}

          {selectedMode === 'map' && (
            <div>
              <Space direction="vertical" style={{ width: '100%' }} size="middle">
                <div>
                  <Text type="secondary">请在地图上点击选择服务地址</Text>
                </div>

                {selectedPosition && (
                  <div style={{ padding: '12px', backgroundColor: '#f6ffed', borderRadius: '6px' }}>
                    <Space direction="vertical" size="small" style={{ width: '100%' }}>
                      <div>
                        <Text strong>地址：</Text>
                        <Text>{selectedPosition.address}</Text>
                      </div>
                      <div>
                        <Text strong>详细地址：</Text>
                        <Input
                          placeholder="请输入详细地址，如门牌号、楼层、房间号等（可选）"
                          value={addressDetail}
                          onChange={(e) => setAddressDetail(e.target.value)}
                          style={{ marginTop: 4 }}
                        />
                      </div>
                      <div>
                        <Text strong>位置备注：</Text>
                        <Input
                          placeholder="请输入位置备注，如最近出入口、显著标志物等（可选）"
                          value={addressRemark}
                          onChange={(e) => setAddressRemark(e.target.value)}
                          style={{ marginTop: 4 }}
                        />
                      </div>
                      <div>
                        <Text strong>经纬度：</Text>
                        <Text>
                          {selectedPosition.longitude.toFixed(6)}, {selectedPosition.latitude.toFixed(6)}
                        </Text>
                      </div>
                    </Space>
                  </div>
                )}

                <div style={{ height: '400px', border: '1px solid #d9d9d9', borderRadius: '6px' }}>
                  <GaoDeMap
                    city="西安"
                    zoom={13}
                    markers={selectedPosition ? [
                      {
                        position: [selectedPosition.longitude, selectedPosition.latitude] as [number, number],
                        title: '选中位置',
                        content: `<div>${selectedPosition.address}</div>`,
                      }
                    ] : []}
                    events={{
                      onClick: handleMapClick,
                    }}
                    style={{ height: '100%', width: '100%' }}
                  />
                </div>

                {mapLoading && (
                  <div style={{ textAlign: 'center' }}>
                    <Text type="secondary">正在解析地址...</Text>
                  </div>
                )}
              </Space>
            </div>
          )}
        </Space>
      </Modal>


    </>
  );
};

export default AddressSelector;
