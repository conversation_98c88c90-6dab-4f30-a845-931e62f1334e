import { getAddressesByCustomer } from '@/services/customer-addresses';
import { reverseGeocode } from '@/components/GaoDeMap/utils';
import { <PERSON><PERSON>, Button, Card, List, Modal, Radio, Space, Typography, message } from 'antd';
import React, { useEffect, useState } from 'react';
import MapSelector from './MapSelector';

const { Text } = Typography;

interface AddressSelectorProps {
  visible: boolean;
  customerId: number;
  onClose: () => void;
  onSelect: (addressData: {
    addressId?: number;
    address: string;
    addressDetail?: string;
    longitude: number;
    latitude: number;
    addressRemark?: string;
  }) => void;
}

/**
 * 地址选择器组件
 * 支持从用户已保存地址选择或在地图上选择新地址
 */
const AddressSelector: React.FC<AddressSelectorProps> = ({
  visible,
  customerId,
  onClose,
  onSelect,
}) => {
  const [loading, setLoading] = useState(false);
  const [addresses, setAddresses] = useState<API.CustomerAddress[]>([]);
  const [selectedMode, setSelectedMode] = useState<'saved' | 'map'>('saved');
  const [selectedAddressId, setSelectedAddressId] = useState<number>();
  const [mapSelectorVisible, setMapSelectorVisible] = useState(false);

  // 获取用户地址列表
  const fetchAddresses = async () => {
    if (!customerId) return;
    
    setLoading(true);
    try {
      const response = await getAddressesByCustomer(customerId);
      if (response.errCode) {
        message.error(response.msg || '获取用户地址失败');
        setAddresses([]);
      } else {
        const addressData = response.data || [];
        console.log('获取到的地址数据:', addressData); // 调试信息
        setAddresses(addressData);
      }
    } catch (error) {
      console.error('获取用户地址失败:', error);
      message.error('获取用户地址失败');
      setAddresses([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible && customerId) {
      fetchAddresses();
    }
  }, [visible, customerId]);

  // 处理选择已保存的地址
  const handleSelectSavedAddress = () => {
    const selectedAddress = addresses.find(addr => addr.id === selectedAddressId);
    if (!selectedAddress) {
      message.warning('请选择一个地址');
      return;
    }

    // 转换经纬度为数字类型
    const lng = typeof selectedAddress.longitude === 'string' ? parseFloat(selectedAddress.longitude) : selectedAddress.longitude;
    const lat = typeof selectedAddress.latitude === 'string' ? parseFloat(selectedAddress.latitude) : selectedAddress.latitude;

    // 检查是否有经纬度信息
    if (!lng || !lat || isNaN(lng) || isNaN(lat)) {
      message.warning('该地址缺少经纬度信息，请提醒用户到地址管理界面通过点选地图补全信息');
      return;
    }

    onSelect({
      addressId: selectedAddress.id,
      address: selectedAddress.addressText || selectedAddress.address || '',
      addressDetail: selectedAddress.addressDetail,
      longitude: lng,
      latitude: lat,
      addressRemark: selectedAddress.addressRemark || selectedAddress.remark,
    });
  };

  // 处理地图选择地址
  const handleMapSelect = (data: {
    address: string;
    longitude: number;
    latitude: number;
  }) => {
    onSelect({
      address: data.address,
      longitude: data.longitude,
      latitude: data.latitude,
    });
    setMapSelectorVisible(false);
  };

  // 处理确认选择
  const handleConfirm = () => {
    if (selectedMode === 'saved') {
      handleSelectSavedAddress();
    } else {
      setMapSelectorVisible(true);
    }
  };

  // 过滤有效地址（有经纬度的地址）
  const validAddresses = addresses.filter(addr => {
    const lng = typeof addr.longitude === 'string' ? parseFloat(addr.longitude) : addr.longitude;
    const lat = typeof addr.latitude === 'string' ? parseFloat(addr.latitude) : addr.latitude;
    const isValid = lng && lat && !isNaN(lng) && !isNaN(lat);
    console.log('地址验证:', {
      id: addr.id,
      addressText: addr.addressText,
      longitude: addr.longitude,
      latitude: addr.latitude,
      parsedLng: lng,
      parsedLat: lat,
      isValid
    }); // 调试信息
    return isValid;
  });
  const invalidAddresses = addresses.filter(addr => {
    const lng = typeof addr.longitude === 'string' ? parseFloat(addr.longitude) : addr.longitude;
    const lat = typeof addr.latitude === 'string' ? parseFloat(addr.latitude) : addr.latitude;
    return !lng || !lat || isNaN(lng) || isNaN(lat);
  });

  console.log('地址过滤结果:', {
    total: addresses.length,
    valid: validAddresses.length,
    invalid: invalidAddresses.length
  }); // 调试信息

  return (
    <>
      <Modal
        title="选择服务地址"
        open={visible}
        onCancel={onClose}
        width={600}
        footer={[
          <Button key="cancel" onClick={onClose}>
            取消
          </Button>,
          <Button key="confirm" type="primary" onClick={handleConfirm}>
            确认选择
          </Button>,
        ]}
      >
        <Space direction="vertical" style={{ width: '100%' }} size="large">
          <Radio.Group
            value={selectedMode}
            onChange={(e) => setSelectedMode(e.target.value)}
          >
            <Radio value="saved">选择用户已保存的地址</Radio>
            <Radio value="map">在地图上选择新地址</Radio>
          </Radio.Group>

          {selectedMode === 'saved' && (
            <div>
              {loading ? (
                <div style={{ textAlign: 'center', padding: '20px' }}>
                  加载中...
                </div>
              ) : (
                <>
                  {validAddresses.length > 0 ? (
                    <List
                      dataSource={validAddresses}
                      renderItem={(address) => (
                        <List.Item>
                          <Card
                            size="small"
                            style={{ width: '100%' }}
                            bodyStyle={{
                              backgroundColor: selectedAddressId === address.id ? '#f6ffed' : undefined,
                              border: selectedAddressId === address.id ? '1px solid #52c41a' : undefined,
                            }}
                            onClick={() => setSelectedAddressId(address.id)}
                          >
                            <Space direction="vertical" size="small" style={{ width: '100%' }}>
                              <div>
                                <Text strong>{address.name || address.addressText || '地址'}</Text>
                              </div>
                              <div>
                                <Text>{address.addressText || address.address}</Text>
                                {address.addressDetail && address.addressDetail !== (address.addressText || address.address) && (
                                  <Text type="secondary"> - {address.addressDetail}</Text>
                                )}
                              </div>
                              {(address.addressRemark || address.remark) && (
                                <div>
                                  <Text type="secondary">备注: {address.addressRemark || address.remark}</Text>
                                </div>
                              )}
                              <div>
                                <Text type="secondary" style={{ fontSize: '12px' }}>
                                  经纬度: {address.longitude}, {address.latitude}
                                </Text>
                              </div>
                            </Space>
                          </Card>
                        </List.Item>
                      )}
                    />
                  ) : (
                    <div style={{ textAlign: 'center', padding: '20px' }}>
                      <Text type="secondary">该用户暂无可用地址</Text>
                    </div>
                  )}

                  {invalidAddresses.length > 0 && (
                    <Alert
                      type="warning"
                      message={`发现 ${invalidAddresses.length} 个地址缺少经纬度信息，无法选择`}
                      description="请提醒用户到地址管理界面通过点选地图补全信息"
                      showIcon
                    />
                  )}
                </>
              )}
            </div>
          )}

          {selectedMode === 'map' && (
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <Text type="secondary">点击"确认选择"在地图上选择地址</Text>
            </div>
          )}
        </Space>
      </Modal>

      <MapSelector
        visible={mapSelectorVisible}
        onClose={() => setMapSelectorVisible(false)}
        onSelect={handleMapSelect}
      />
    </>
  );
};

export default AddressSelector;
