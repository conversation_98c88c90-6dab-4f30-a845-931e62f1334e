import AddressSelector from '@/components/AddressSelector';
import { Button, Card, Space, Typography } from 'antd';
import React, { useState } from 'react';

const { Title, Text } = Typography;

/**
 * 地址选择器测试页面
 */
const AddressSelectorTest: React.FC = () => {
  const [visible, setVisible] = useState(false);
  const [selectedAddress, setSelectedAddress] = useState<any>();
  
  // 测试用的客户ID，您可以替换为实际的客户ID
  const testCustomerId = 1;

  const handleAddressSelect = (addressData: any) => {
    console.log('选择的地址:', addressData);
    setSelectedAddress(addressData);
    setVisible(false);
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <Title level={3}>地址选择器测试</Title>
          
          <div>
            <Text>测试客户ID: {testCustomerId}</Text>
          </div>

          <Button 
            type="primary" 
            onClick={() => setVisible(true)}
          >
            打开地址选择器
          </Button>

          {selectedAddress && (
            <Card title="选择的地址信息" size="small">
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <div>
                  <Text strong>地址ID: </Text>
                  <Text>{selectedAddress.addressId || '无'}</Text>
                </div>
                <div>
                  <Text strong>地址: </Text>
                  <Text>{selectedAddress.address}</Text>
                </div>
                {selectedAddress.addressDetail && (
                  <div>
                    <Text strong>详细地址: </Text>
                    <Text>{selectedAddress.addressDetail}</Text>
                  </div>
                )}
                <div>
                  <Text strong>经度: </Text>
                  <Text>{selectedAddress.longitude}</Text>
                </div>
                <div>
                  <Text strong>纬度: </Text>
                  <Text>{selectedAddress.latitude}</Text>
                </div>
                {selectedAddress.addressRemark && (
                  <div>
                    <Text strong>备注: </Text>
                    <Text>{selectedAddress.addressRemark}</Text>
                  </div>
                )}
              </Space>
            </Card>
          )}
        </Space>
      </Card>

      <AddressSelector
        visible={visible}
        customerId={testCustomerId}
        onClose={() => setVisible(false)}
        onSelect={handleAddressSelect}
      />
    </div>
  );
};

export default AddressSelectorTest;
