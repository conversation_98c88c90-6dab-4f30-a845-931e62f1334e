# 订单服务地址修改功能演示

## 功能入口

新增的订单服务地址修改功能在以下位置提供了入口：

### 1. 订单详情页面
- 路径：订单管理 → 查看详情 → 预约地址部分
- 操作：点击"修改地址"按钮

### 2. 订单列表页面
- 路径：订单管理 → 详细列表 → 操作列
- 操作：点击"修改地址"按钮

### 3. 状态看板视图
- 路径：订单管理 → 状态看板 → 订单卡片操作区
- 操作：点击"修改地址"按钮

### 4. 时间安排视图
- 路径：订单管理 → 时间安排 → 订单列表操作区
- 操作：点击"修改地址"按钮

## 功能流程演示

### 步骤1：打开地址修改界面
1. 在任意订单管理视图中找到目标订单
2. 点击"修改地址"按钮
3. 系统打开"修改订单服务地址"模态框

### 步骤2：查看当前信息
模态框显示以下信息：
- **订单基本信息**：订单编号、状态、客户信息、服务时间
- **当前服务地址**：包含地址解析、详细地址、地址备注
- **权限说明**：管理端可修改任何状态订单的地址

### 步骤3：选择新地址
点击"点击选择新的服务地址"按钮，打开地址选择器：

#### 选项A：选择用户已保存的地址
1. 选择"选择用户已保存的地址"单选框
2. 系统显示该用户的所有有效地址列表
3. 有效地址（包含经纬度）显示为可选择的卡片
4. 无效地址显示警告提示
5. 点击选择一个有效地址
6. 点击"确认选择"

#### 选项B：在地图上选择新地址
1. 选择"在地图上选择新地址"单选框
2. 点击"确认选择"打开地图选择器
3. 在高德地图上点击任意位置
4. 系统自动解析该位置的详细地址
5. 显示选中位置的地址和经纬度信息
6. 点击"确认选择"

### 步骤4：确认修改
1. 返回地址修改模态框，查看选中的新地址信息
2. 确认地址信息正确
3. 点击"确认修改"按钮
4. 系统调用后端API更新订单地址
5. 显示成功提示并关闭模态框
6. 订单列表自动刷新显示最新数据

## 特殊情况处理

### 1. 用户无有效地址
- **现象**：用户地址列表为空或全部地址缺少经纬度
- **处理**：显示"该用户暂无可用地址"提示
- **建议**：使用地图选择新地址

### 2. 地址缺少经纬度信息
- **现象**：用户地址列表中某些地址显示警告
- **处理**：这些地址无法选择，显示警告信息
- **提示**："请提醒用户到地址管理界面通过点选地图补全信息"

### 3. 地图API加载失败
- **现象**：地图无法正常显示
- **处理**：显示错误提示，但不影响其他功能
- **建议**：检查网络连接或稍后重试

### 4. 网络请求失败
- **现象**：API调用失败
- **处理**：显示具体错误信息
- **建议**：检查网络连接并重试

## 权限说明

### 管理端权限
- 可以修改**任何状态**的订单地址
- 支持各种售后服务场景
- 无状态限制

### 员工端权限（未来扩展）
- 只能修改**出发前**的订单地址
- 适用状态：待付款、待接单、待服务
- 已出发后无法修改

### 用户端权限（未来扩展）
- 只能修改**出发前**的订单地址
- 适用状态：待付款、待接单、待服务
- 已出发后无法修改

## 数据更新机制

### 实时刷新
- 地址修改成功后，所有相关视图自动刷新
- 确保数据一致性

### 地址解析
- 使用高德地图API进行实时地址解析
- 支持经纬度到详细地址的转换
- 提供地址预览功能

### 数据验证
- 前端验证地址完整性（经纬度必填）
- 后端验证用户权限和订单状态
- 确保数据安全性

## 用户体验优化

### 1. 加载状态
- 地址列表加载时显示loading状态
- 地址解析时显示"正在解析地址..."提示
- API请求时按钮显示loading状态

### 2. 错误处理
- 友好的错误提示信息
- 具体的操作建议
- 不影响其他功能的正常使用

### 3. 界面反馈
- 成功操作显示绿色提示
- 错误操作显示红色提示
- 警告信息显示橙色提示

### 4. 操作便捷性
- 一键打开地址修改功能
- 直观的地址选择界面
- 清晰的操作流程指引

## 技术特性

### 1. 组件化设计
- 地址选择器可复用
- 地图选择器独立封装
- 模态框组件化

### 2. API集成
- 统一的错误处理
- 标准的响应格式
- 完善的权限控制

### 3. 地图集成
- 高德地图API集成
- 实时地址解析
- 交互式地图选择

### 4. 状态管理
- React Hooks状态管理
- 组件间数据传递
- 实时数据同步
