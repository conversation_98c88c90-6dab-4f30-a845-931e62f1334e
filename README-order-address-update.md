# 订单服务地址修改功能

## 功能简介

为管理端新增了订单服务地址修改功能，支持管理员在任何订单状态下修改服务地址，以满足各种售后服务需求。

## 主要特性

- ✅ **多入口支持**：订单详情、列表、状态看板、时间安排视图均可修改地址
- ✅ **双重选择方式**：支持选择用户已保存地址或在地图上选择新地址
- ✅ **智能验证**：自动过滤无效地址，确保地址包含必要的经纬度信息
- ✅ **权限控制**：管理端可修改任何状态订单，支持售后服务场景
- ✅ **实时解析**：集成高德地图API，实时解析地址信息
- ✅ **用户友好**：完善的错误处理、加载状态、操作反馈

## 快速开始

### 1. 功能入口

在以下位置可以找到"修改地址"功能：

- **订单详情页**：预约地址部分的"修改地址"按钮
- **订单列表页**：操作列的"修改地址"按钮
- **状态看板**：订单卡片的"修改地址"按钮
- **时间安排**：订单列表的"修改地址"按钮

### 2. 使用流程

1. 点击"修改地址"按钮
2. 查看当前订单和地址信息
3. 选择新地址：
   - **方式A**：从用户已保存地址中选择
   - **方式B**：在地图上点击选择新位置
4. 确认地址信息
5. 点击"确认修改"完成更新

## 技术架构

### 新增组件

```
src/components/AddressSelector/
├── index.tsx          # 地址选择器主组件
└── MapSelector.tsx    # 地图地址选择器

src/pages/Appointment/
└── UpdateAddressModal.tsx  # 订单地址修改模态框

src/services/
└── customer-addresses.ts   # 用户地址API服务
```

### 集成点

- `src/pages/Appointment/DetailModal.tsx` - 订单详情页集成
- `src/pages/Appointment/index.tsx` - 订单管理主页集成
- `src/pages/Appointment/components/StatusBoardView.tsx` - 状态看板集成
- `src/pages/Appointment/components/TimelineView.tsx` - 时间安排集成

## API依赖

### 必需接口

1. **订单地址修改**
   ```
   PUT /orders/{orderId}/updateServiceAddress
   ```

2. **用户地址列表**
   ```
   GET /customer-addresses/customer/{customerId}
   ```

### 第三方服务

- **高德地图API**：用于地图显示和地址解析
- **腾讯云存储**：用于相关文件存储（如需要）

## 配置要求

### 环境变量

确保以下配置正确：

```javascript
// config/config.ts
AMAP_CONFIG: {
  key: 'your-amap-api-key',
  securityJsCode: 'your-security-js-code',
}
```

### 权限配置

确保管理端用户具有以下权限：
- 订单管理权限
- 地址修改权限
- 用户数据访问权限

## 开发指南

### 本地开发

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 类型检查
npm run type-check

# 构建项目
npm run build
```

### 测试

参考 `docs/testing-guide.md` 进行完整的功能测试。

### 部署

参考 `docs/deployment-checklist.md` 进行部署前检查。

## 文档

- [功能详细说明](docs/order-address-update.md)
- [测试指南](docs/testing-guide.md)
- [功能演示](docs/feature-demo.md)
- [实现总结](docs/order-address-update-summary.md)
- [部署检查清单](docs/deployment-checklist.md)

## 故障排除

### 常见问题

1. **地图无法显示**
   - 检查高德地图API Key配置
   - 确认域名在白名单中
   - 检查网络连接

2. **地址列表为空**
   - 确认用户有已保存的地址
   - 检查地址是否包含经纬度信息
   - 验证API接口是否正常

3. **地址修改失败**
   - 检查用户权限
   - 确认订单状态
   - 验证后端API接口

### 错误日志

查看浏览器控制台和网络请求，关键错误信息：
- `获取用户地址失败` - 用户地址API问题
- `地址解析失败` - 高德地图API问题
- `修改地址失败` - 订单地址修改API问题

## 版本历史

### v1.0.0 (2024-12-30)
- ✅ 初始版本发布
- ✅ 支持管理端地址修改
- ✅ 集成高德地图选择
- ✅ 完整的错误处理
- ✅ 多入口支持

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请联系开发团队。

---

**注意**：使用前请确保所有依赖的后端API已正确部署并配置。
