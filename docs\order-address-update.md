# 订单服务地址修改功能

## 功能概述

管理端新增了订单服务地址修改功能，支持管理员在任何订单状态下修改服务地址，以支持各种售后服务需求。

## 功能特性

### 1. 权限控制
- **管理端（admin）**：可在任何订单状态下修改服务地址
- **员工端（employee）**：只能在出发前（待付款、待接单、待服务）修改服务地址
- **用户端（customer）**：只能在出发前（待付款、待接单、待服务）修改服务地址

### 2. 地址选择方式
支持两种地址选择方式：

#### 方式一：选择用户已保存的地址
- 显示用户所有已保存的地址列表
- 自动过滤掉缺少经纬度信息的地址
- 对于无效地址，会提示用户到地址管理界面补全信息

#### 方式二：在地图上选择新地址
- 在高德地图上点击选择位置
- 自动进行逆地理编码获取详细地址
- 显示选中位置的经纬度坐标

### 3. 地址验证
- 检查地址是否包含必要的经纬度信息
- 对于缺少经纬度的地址，会显示警告并阻止选择
- 提示用户到地址管理界面通过地图补全信息

## 使用方法

### 在订单详情中修改地址
1. 打开订单详情页面
2. 在"预约地址"部分点击"修改地址"按钮
3. 选择地址选择方式（用户已保存地址 或 地图选择）
4. 确认选择新地址
5. 点击"确认修改"完成地址更新

### 在订单列表中修改地址
1. 在订单管理列表页面
2. 点击对应订单行的"修改地址"操作按钮
3. 按照地址选择流程完成修改

## 技术实现

### 新增文件
- `src/services/customer-addresses.ts` - 用户地址API服务
- `src/components/AddressSelector/index.tsx` - 地址选择器主组件
- `src/components/AddressSelector/MapSelector.tsx` - 地图地址选择器
- `src/pages/Appointment/UpdateAddressModal.tsx` - 订单地址修改模态框

### 修改文件
- `src/pages/Appointment/DetailModal.tsx` - 订单详情页面，添加修改地址功能
- `src/pages/Appointment/index.tsx` - 订单管理主页面，添加修改地址操作

### API接口
使用后端提供的 `PUT /orders/{orderId}/updateServiceAddress` 接口进行地址更新。

## 注意事项

1. **地址完整性检查**：系统会检查用户地址是否包含经纬度信息，缺少的地址无法选择
2. **权限控制**：管理端可以修改任何状态的订单地址，支持售后服务场景
3. **地址解析**：使用高德地图API进行地址解析和显示
4. **数据一致性**：修改地址后会自动刷新订单列表，确保数据同步

## 错误处理

- 网络请求失败时显示友好的错误提示
- 地址解析失败时允许重新选择
- 权限不足时显示相应的错误信息
- 参数验证失败时提供具体的错误说明
