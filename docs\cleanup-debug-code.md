# 清理调试代码指南

## 生产环境部署前的清理工作

在部署到生产环境之前，需要清理所有调试代码以提高性能和安全性。

## 需要清理的文件和内容

### 1. src/components/AddressSelector/index.tsx

**已注释的调试代码**（生产环境请完全移除）：

```typescript
// 第49行附近 - 移除地址数据日志
// console.log('获取到的地址数据:', addressData);

// 第131-141行附近 - 移除地址验证日志
// console.log('地址验证:', {
//   id: addr.id,
//   addressText: addr.addressText,
//   longitude: addr.longitude,
//   latitude: addr.latitude,
//   parsedLng: lng,
//   parsedLat: lat,
//   isValid
// });

// 第149-153行附近 - 移除过滤结果日志
// console.log('地址过滤结果:', {
//   total: addresses.length,
//   valid: validAddresses.length,
//   invalid: invalidAddresses.length
// });
```

### 2. src/pages/Test/AddressSelectorTest.tsx

**整个测试文件**（生产环境请移除）：
- 这是专门用于测试的页面，生产环境不需要

## 清理步骤

### 自动清理（推荐）

使用以下命令清理所有注释的调试代码：

```bash
# 移除注释的console.log
find src -name "*.tsx" -o -name "*.ts" | xargs sed -i '/\/\/ console\.log/d'

# 移除多行注释的调试代码
find src -name "*.tsx" -o -name "*.ts" | xargs sed -i '/\/\/ console\.log/,/\/\/ });/d'
```

### 手动清理

1. **搜索所有console.log**
   ```bash
   grep -r "console.log" src/
   ```

2. **搜索所有调试注释**
   ```bash
   grep -r "调试信息" src/
   grep -r "// 调试" src/
   ```

3. **移除测试文件**
   ```bash
   rm -rf src/pages/Test/
   ```

### 验证清理结果

清理完成后，运行以下命令验证：

```bash
# 确保没有console.log残留
grep -r "console.log" src/ | grep -v "console.error"

# 确保没有调试注释残留
grep -r "调试信息\|调试代码\|debug\|DEBUG" src/

# 构建测试
npm run build
```

## 保留的日志

以下日志应该保留，因为它们用于错误处理：

```typescript
// 错误日志 - 保留
console.error('获取用户地址失败:', error);
console.error('地址解析失败:', error);
console.error('修改地址失败:', error);
```

## 性能优化建议

清理调试代码后，还可以进行以下优化：

### 1. 移除未使用的导入
```bash
# 使用ESLint检查未使用的导入
npx eslint src/ --fix
```

### 2. 代码压缩
确保生产构建启用了代码压缩：
```javascript
// config/config.ts
export default defineConfig({
  // ...其他配置
  define: {
    'process.env.NODE_ENV': 'production',
  },
});
```

### 3. 移除开发依赖
确保package.json中的开发依赖不会被打包到生产环境。

## 检查清单

部署前请确认：

- [ ] 移除所有console.log调试信息
- [ ] 移除所有调试注释
- [ ] 移除测试页面和文件
- [ ] 保留必要的错误日志
- [ ] 运行构建测试成功
- [ ] 代码压缩正常工作
- [ ] 没有未使用的导入

## 回滚方案

如果清理后出现问题，可以：

1. **从Git恢复**
   ```bash
   git checkout HEAD -- src/components/AddressSelector/index.tsx
   ```

2. **重新启用调试模式**
   - 取消注释调试代码
   - 重新添加console.log语句

3. **分步清理**
   - 先清理一个文件，测试无误后再清理下一个
   - 每次清理后都进行构建测试

## 注意事项

1. **不要清理错误处理日志** - console.error应该保留
2. **保留必要的警告信息** - 用户操作相关的警告应该保留
3. **测试清理结果** - 清理后务必进行完整的功能测试
4. **备份代码** - 清理前确保代码已提交到版本控制系统
