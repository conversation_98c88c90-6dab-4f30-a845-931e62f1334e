# 订单地址修改功能测试指南

## 测试前准备

1. 确保后端API `PUT /orders/{orderId}/updateServiceAddress` 已部署并可用
2. 确保用户地址API `GET /customer-addresses/customer/{customerId}` 已部署并可用
3. 确保高德地图API配置正确且可用

## 功能测试步骤

### 1. 在订单详情中测试地址修改

#### 测试步骤：
1. 登录管理端系统
2. 进入订单管理页面
3. 点击任意订单的"查看详情"按钮
4. 在订单详情页面的"预约地址"部分，点击"修改地址"按钮
5. 在弹出的地址修改模态框中，验证以下内容：
   - 显示当前订单的基本信息
   - 显示当前的服务地址信息
   - 提供地址选择功能

#### 预期结果：
- 模态框正常打开
- 订单信息显示正确
- 当前地址信息显示正确

### 2. 测试用户已保存地址选择

#### 测试步骤：
1. 在地址修改模态框中，点击"点击选择新的服务地址"
2. 在地址选择器中，选择"选择用户已保存的地址"
3. 验证地址列表显示：
   - 显示用户的所有有效地址（有经纬度信息）
   - 对于无效地址，显示警告提示
4. 选择一个有效地址
5. 点击"确认选择"
6. 验证选中的地址信息显示在修改模态框中
7. 点击"确认修改"

#### 预期结果：
- 地址列表正确显示
- 无效地址被过滤并显示警告
- 地址选择功能正常
- 地址修改成功，页面刷新

### 3. 测试地图选择地址

#### 测试步骤：
1. 在地址选择器中，选择"在地图上选择新地址"
2. 点击"确认选择"打开地图选择器
3. 在地图上点击任意位置
4. 验证地址解析功能：
   - 显示选中位置的详细地址
   - 显示经纬度坐标
5. 点击"确认选择"
6. 验证选中的地址信息显示在修改模态框中
7. 点击"确认修改"

#### 预期结果：
- 地图正常加载
- 点击位置能正确解析地址
- 地址信息正确显示
- 地址修改成功

### 4. 测试订单列表中的地址修改

#### 测试步骤：
1. 在订单管理列表页面
2. 点击任意订单行的"修改地址"按钮
3. 按照上述步骤测试地址选择和修改功能

#### 预期结果：
- 功能与订单详情中的修改功能一致
- 修改成功后列表自动刷新

## 错误场景测试

### 1. 网络错误测试
- 断开网络连接，测试各API调用的错误处理
- 预期：显示友好的错误提示

### 2. 无效地址测试
- 选择缺少经纬度信息的地址
- 预期：显示警告并阻止选择

### 3. 权限测试
- 使用不同角色的用户测试功能
- 预期：管理员可以修改任何状态的订单地址

### 4. 地图API错误测试
- 模拟高德地图API加载失败
- 预期：显示错误提示，不影响其他功能

## 性能测试

### 1. 大量地址列表测试
- 测试用户有大量保存地址时的性能
- 预期：列表加载流畅，无明显卡顿

### 2. 地图加载性能测试
- 测试地图组件的加载速度
- 预期：地图在合理时间内加载完成

## 兼容性测试

### 1. 浏览器兼容性
- 在不同浏览器中测试功能
- 预期：主流浏览器均能正常使用

### 2. 移动端适配
- 在移动设备上测试功能
- 预期：界面适配良好，操作流畅

## 测试完成检查清单

- [ ] 订单详情中的地址修改功能正常
- [ ] 订单列表中的地址修改功能正常
- [ ] 用户已保存地址选择功能正常
- [ ] 地图选择地址功能正常
- [ ] 地址验证功能正常
- [ ] 错误处理功能正常
- [ ] 权限控制功能正常
- [ ] 页面刷新功能正常
- [ ] 无console错误或警告
- [ ] 性能表现良好

## 注意事项

1. 测试过程中注意观察浏览器控制台，确保没有JavaScript错误
2. 测试不同订单状态下的地址修改功能
3. 验证修改后的地址信息是否正确保存到数据库
4. 确保所有用户交互都有适当的反馈（loading状态、成功/失败提示等）
